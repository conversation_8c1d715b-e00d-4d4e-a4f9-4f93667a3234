package asesdk

import (
	"context"
	"fmt"
	"testing"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools"
	aseclient "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/ase_sdk/client"
)

func TestAseSdk(t *testing.T) {

	err := goboot.InitFromConfig("./ase.toml")
	if err != nil {
		panic(err)
	}

	bc := boot_tools.NewBootContext("asetest")
	goboot.AseSdk().Must() //或err = goboot.NeedRedis("redis1")

	req := &aseclient.InferReq{}

	span := bc.RootSpan().AddSpan("ase")
	defer span.Finish()

	s, err := goboot.AseSdk().DefaultInstance().Request().
		SetParentSpan(span).Send(context.TODO(), req)
	fmt.Println(s, err)
}
